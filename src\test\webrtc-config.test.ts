/**
 * WebRTC 配置功能测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useVideoChatStore } from '@/store'
import { 
  DEFAULT_WEBRTC_CONFIG, 
  RECEIVE_ONLY_CONFIG, 
  WebRTCConfig 
} from '@/interface/voiceChat'

describe('WebRTC Configuration', () => {
  beforeEach(() => {
    // 为每个测试创建新的 Pinia 实例
    setActivePinia(createPinia())
  })

  it('should initialize with default config', () => {
    const store = useVideoChatStore()
    
    expect(store.webrtcConfig).toEqual(DEFAULT_WEBRTC_CONFIG)
    expect(store.webrtcConfig.enableLocalStream).toBe(true)
    expect(store.webrtcConfig.autoRequestPermissions).toBe(true)
  })

  it('should allow setting receive-only config', () => {
    const store = useVideoChatStore()
    
    store.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
    
    expect(store.webrtcConfig.enableLocalStream).toBe(false)
    expect(store.webrtcConfig.enableLocalVideo).toBe(false)
    expect(store.webrtcConfig.enableLocalAudio).toBe(false)
    expect(store.webrtcConfig.autoRequestPermissions).toBe(false)
    expect(store.webrtcConfig.showLocalVideo).toBe(false)
    expect(store.webrtcConfig.showDeviceControls).toBe(false)
  })

  it('should allow partial config updates', () => {
    const store = useVideoChatStore()
    
    // 只更新部分配置
    store.setWebRTCConfig({
      enableLocalVideo: false,
      showLocalVideo: false,
    })
    
    // 其他配置应该保持默认值
    expect(store.webrtcConfig.enableLocalStream).toBe(true)
    expect(store.webrtcConfig.enableLocalAudio).toBe(true)
    expect(store.webrtcConfig.autoRequestPermissions).toBe(true)
    expect(store.webrtcConfig.showDeviceControls).toBe(true)
    
    // 更新的配置应该生效
    expect(store.webrtcConfig.enableLocalVideo).toBe(false)
    expect(store.webrtcConfig.showLocalVideo).toBe(false)
  })

  it('should support audio-only configuration', () => {
    const store = useVideoChatStore()
    
    const audioOnlyConfig: Partial<WebRTCConfig> = {
      enableLocalVideo: false,
      showLocalVideo: false,
    }
    
    store.setWebRTCConfig(audioOnlyConfig)
    
    expect(store.webrtcConfig.enableLocalStream).toBe(true)
    expect(store.webrtcConfig.enableLocalAudio).toBe(true)
    expect(store.webrtcConfig.enableLocalVideo).toBe(false)
    expect(store.webrtcConfig.showLocalVideo).toBe(false)
  })

  it('should handle device access based on config', async () => {
    const store = useVideoChatStore()
    
    // 模拟 navigator.mediaDevices
    const mockGetUserMedia = vi.fn()
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: mockGetUserMedia,
      },
      writable: true,
    })

    // 测试仅接收模式 - 不应该请求权限
    store.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
    await store.accessDevice()
    
    expect(mockGetUserMedia).not.toHaveBeenCalled()
    expect(store.webcamAccessed).toBe(true)
  })

  it('should handle fillStream based on config', async () => {
    const store = useVideoChatStore()
    
    // 测试仅接收模式 - 应该创建空流
    store.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
    await store.fillStream('', '')
    
    expect(store.stream).toBeInstanceOf(MediaStream)
    expect(store.stream?.getTracks().length).toBe(0)
    expect(store.webcamAccessed).toBe(true)
  })

  it('should validate config properties', () => {
    const store = useVideoChatStore()
    
    // 测试所有配置属性都存在
    const config = store.webrtcConfig
    
    expect(typeof config.enableLocalStream).toBe('boolean')
    expect(typeof config.enableLocalVideo).toBe('boolean')
    expect(typeof config.enableLocalAudio).toBe('boolean')
    expect(typeof config.autoRequestPermissions).toBe('boolean')
    expect(typeof config.showLocalVideo).toBe('boolean')
    expect(typeof config.showDeviceControls).toBe('boolean')
  })

  it('should maintain config consistency', () => {
    const store = useVideoChatStore()
    
    // 设置不一致的配置
    store.setWebRTCConfig({
      enableLocalStream: false,
      enableLocalVideo: true, // 这在逻辑上不一致，但应该被允许
    })
    
    // 配置应该按设置的值保存
    expect(store.webrtcConfig.enableLocalStream).toBe(false)
    expect(store.webrtcConfig.enableLocalVideo).toBe(true)
  })
})

// 集成测试
describe('WebRTC Configuration Integration', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should work with different URL parameters', () => {
    // 模拟不同的 URL 参数
    const testCases = [
      { mode: 'receive-only', expectedConfig: RECEIVE_ONLY_CONFIG },
      { mode: 'default', expectedConfig: DEFAULT_WEBRTC_CONFIG },
    ]

    testCases.forEach(({ mode, expectedConfig }) => {
      const store = useVideoChatStore()
      
      // 根据模式设置配置
      if (mode === 'receive-only') {
        store.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
      }
      
      // 验证配置
      Object.keys(expectedConfig).forEach(key => {
        expect(store.webrtcConfig[key as keyof WebRTCConfig])
          .toBe(expectedConfig[key as keyof WebRTCConfig])
      })
    })
  })
})
