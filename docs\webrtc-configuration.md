# WebRTC 配置指南

本文档介绍如何配置 OpenAvatarChat-WebUI 的 WebRTC 功能，支持不同的使用场景。

## 概述

新版本支持通过配置参数控制 WebRTC 的行为，可以实现：
- **仅接收模式**：只接收远程流，不发送本地流
- **双向通信模式**：发送和接收音视频流（默认）
- **自定义模式**：灵活配置各种组合

## 配置接口

```typescript
interface WebRTCConfig {
  enableLocalStream: boolean      // 是否启用本地流（摄像头/麦克风）
  enableLocalVideo: boolean       // 是否启用本地视频
  enableLocalAudio: boolean       // 是否启用本地音频
  autoRequestPermissions: boolean // 是否自动请求设备权限
  showLocalVideo: boolean         // 是否显示本地视频预览
  showDeviceControls: boolean     // 是否显示设备控制按钮
}
```

## 预设配置

### 1. 默认配置（双向模式）
```typescript
const DEFAULT_WEBRTC_CONFIG = {
  enableLocalStream: true,
  enableLocalVideo: true,
  enableLocalAudio: true,
  autoRequestPermissions: true,
  showLocalVideo: true,
  showDeviceControls: true,
}
```

### 2. 仅接收模式
```typescript
const RECEIVE_ONLY_CONFIG = {
  enableLocalStream: false,
  enableLocalVideo: false,
  enableLocalAudio: false,
  autoRequestPermissions: false,
  showLocalVideo: false,
  showDeviceControls: false,
}
```

## 使用方法

### 基本用法

```typescript
import { useVideoChatStore } from '@/store'
import { RECEIVE_ONLY_CONFIG } from '@/interface/voiceChat'

// 获取 store 实例
const videoChatStore = useVideoChatStore()

// 设置为仅接收模式
videoChatStore.setWebRTCConfig(RECEIVE_ONLY_CONFIG)

// 初始化设备访问
videoChatStore.accessDevice()
```

### 自定义配置

```typescript
// 仅音频模式
const audioOnlyConfig = {
  enableLocalStream: true,
  enableLocalVideo: false,
  enableLocalAudio: true,
  autoRequestPermissions: true,
  showLocalVideo: false,
  showDeviceControls: true,
}

videoChatStore.setWebRTCConfig(audioOnlyConfig)
```

### 通过 URL 参数配置

```typescript
// 在应用初始化时根据 URL 参数设置模式
const urlParams = new URLSearchParams(window.location.search)
const mode = urlParams.get('mode')

switch (mode) {
  case 'receive-only':
    videoChatStore.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
    break
  case 'audio-only':
    videoChatStore.setWebRTCConfig(audioOnlyConfig)
    break
  default:
    // 使用默认配置
    break
}
```

## 使用场景

### 1. 仅接收模式
适用于：
- 观看直播或演示
- 单向内容消费
- 节省带宽和设备资源

```typescript
// URL: https://your-app.com?mode=receive-only
videoChatStore.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
```

### 2. 仅音频模式
适用于：
- 语音通话
- 节省视频带宽
- 隐私保护

```typescript
const audioOnlyConfig = {
  enableLocalStream: true,
  enableLocalVideo: false,
  enableLocalAudio: true,
  autoRequestPermissions: true,
  showLocalVideo: false,
  showDeviceControls: true,
}
```

### 3. 本地预览模式
适用于：
- 设备测试
- 用户界面展示
- 不需要发送流的场景

```typescript
const previewOnlyConfig = {
  enableLocalStream: false,
  enableLocalVideo: true,
  enableLocalAudio: false,
  autoRequestPermissions: true,
  showLocalVideo: true,
  showDeviceControls: true,
}
```

## 配置说明

### enableLocalStream
- `true`: 启用本地流，会发送音视频到远程
- `false`: 禁用本地流，仅接收远程流

### enableLocalVideo / enableLocalAudio
- 控制具体的媒体类型
- 只有在 `enableLocalStream: true` 时才有效

### autoRequestPermissions
- `true`: 自动请求摄像头/麦克风权限
- `false`: 跳过权限请求，适用于仅接收模式

### showLocalVideo
- `true`: 显示本地视频预览
- `false`: 隐藏本地视频预览

### showDeviceControls
- `true`: 显示设备控制按钮（摄像头开关、麦克风开关等）
- `false`: 隐藏设备控制按钮

## 注意事项

1. **向后兼容性**：默认配置保持原有行为，现有代码无需修改
2. **动态切换**：可以在运行时切换配置，但建议在连接建立前设置
3. **权限管理**：仅接收模式不会请求设备权限，提升用户体验
4. **UI 适配**：界面会根据配置自动显示/隐藏相关元素

## 示例代码

完整的使用示例请参考：`src/examples/webrtc-config-examples.ts`
