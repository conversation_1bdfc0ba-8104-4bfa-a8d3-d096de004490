import { WS } from '@/helpers/ws'; // WebSocket 封装类
import { WsEventTypes } from '@/interface/eventType'; // WebSocket 事件类型枚举
import { StreamState } from '@/interface/voiceChat'; // 流状态枚举
import { GaussianAvatar } from '@/utils/gaussianAvatar'; // 高斯渲染头像工具
import {
  createSimulatedAudioTrack, // 创建模拟音轨（无麦克风时用）
  createSimulatedVideoTrack, // 创建模拟视频轨（无摄像头时用）
  getDevices, // 获取设备列表（摄像头、麦克风）
  getStream, // 获取媒体流（摄像头/麦克风）
  setAvailableDevices, // 筛选可用设备
} from '@/utils/streamUtils';
import { setupWebRTC, stop } from '@/utils/webrtcUtils'; // WebRTC 建立/断开工具
import { message } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { useVisionStore } from './vision';

// 默认媒体流约束（分辨率 500x500）
const track_constraints = {
  video: {
    width: 500,
    height: 500,
  },
  audio: true,
};

// 视频通话的状态结构
interface VideoChatState {
  devices: MediaDeviceInfo[]; // 所有设备
  availableVideoDevices: MediaDeviceInfo[]; // 可用摄像头
  availableAudioDevices: MediaDeviceInfo[]; // 可用麦克风
  selectedVideoDevice: MediaDeviceInfo | null; // 当前使用的摄像头
  selectedAudioDevice: MediaDeviceInfo | null; // 当前使用的麦克风
  streamState: StreamState; // 流状态（open/closed/waiting）
  stream: MediaStream | null; // 本地流（可能含音视频）
  peerConnection: RTCPeerConnection | null; // WebRTC 连接对象
  localStream: MediaStream | null; // 本地流（供 video 标签播放）
  webcamAccessed: boolean; // 摄像头是否已被访问
  webRTCId: string; // WebRTC 会话 ID

  avatarType: '' | 'lam'; // 头像类型（lam 表示高斯头像）
  avatarWSRoute: string; // 头像 WebSocket 路径
  avatarAssetsPath: string; // 头像资源路径
  rtcConfig: RTCConfiguration | undefined; // WebRTC 配置
  trackConstraints:
    | { video: MediaTrackConstraints | boolean; audio: MediaTrackConstraints | boolean }
    | undefined;
  gsLoadPercent: number; // 高斯头像加载进度

  volumeMuted: boolean; // 是否静音播放（扬声器）
  micMuted: boolean; // 是否静音麦克风
  cameraOff: boolean; // 摄像头是否关闭

  hasCamera: boolean; // 是否有摄像头
  hasCameraPermission: boolean; // 是否有摄像头权限
  hasMic: boolean; // 是否有麦克风
  hasMicPermission: boolean; // 是否有麦克风权限
  showChatRecords: boolean; // 是否显示聊天字幕

  localAvatarRenderer: any; // 本地头像渲染对象（lam 时使用）

  chatDataChannel: RTCDataChannel | null; // WebRTC 数据通道
  replying: boolean; // 是否正在回复中
  chatRecords: Array<{ id: string; role: 'human' | 'avatar'; message: string }>; // 聊天记录
}

export const useVideoChatStore = defineStore('videoChatStore', {
  state: (): VideoChatState => ({
    devices: [],
    availableVideoDevices: [],
    availableAudioDevices: [],
    selectedVideoDevice: null,
    selectedAudioDevice: null,
    streamState: StreamState.closed,
    stream: null,
    peerConnection: null,
    localStream: null,
    webRTCId: '',
    webcamAccessed: false,
    avatarType: '',
    avatarWSRoute: '',
    avatarAssetsPath: '',
    rtcConfig: undefined,
    trackConstraints: track_constraints,
    gsLoadPercent: 0,
    volumeMuted: false,
    micMuted: false,
    cameraOff: false,
    hasCamera: false,
    hasCameraPermission: true,
    hasMic: false,
    hasMicPermission: true,
    showChatRecords: false,
    localAvatarRenderer: null,
    chatDataChannel: null,
    replying: false,
    chatRecords: [],
  }),
  getters: {},
  actions: {
    // 访问设备（请求摄像头 & 麦克风权限，并更新设备列表）
    async accessDevice() {
      try {
        const visionState = useVisionStore();
        const node = visionState.localVideoRef;
        this.micMuted = false;
        this.cameraOff = false;
        this.volumeMuted = false;

        // 检查浏览器是否支持媒体设备
        if (!navigator.mediaDevices) {
          message.error('无法获取媒体设备，请确保用localhost访问或https协议访问');
          return;
        }

        // 先尝试获取麦克风权限
        await navigator.mediaDevices.getUserMedia({ audio: true }).catch(() => {
          console.log('no audio permission');
          this.hasMicPermission = false;
        });
        // 再尝试获取摄像头权限
        await navigator.mediaDevices.getUserMedia({ video: true }).catch(() => {
          console.log('no video permission');
          this.hasCameraPermission = false;
        });

        // 获取设备列表
        const devices = await getDevices();
        this.devices = devices;

        // 保持之前选中的设备
        const videoDeviceId =
          this.selectedVideoDevice &&
          devices.some((d) => d.deviceId === this.selectedVideoDevice?.deviceId)
            ? this.selectedVideoDevice.deviceId
            : '';
        const audioDeviceId =
          this.selectedAudioDevice &&
          devices.some((d) => d.deviceId === this.selectedAudioDevice?.deviceId)
            ? this.selectedAudioDevice.deviceId
            : '';

        // 填充媒体流
        this.fillStream(audioDeviceId, videoDeviceId);
        this.webcamAccessed = true;
      } catch (err: any) {
        console.log(err);
        message.error(err.message);
      }
    },
    // 从后端拉取初始化配置
    async init() {
      fetch('/openavatarchat/init')
        .then((res) => res.json())
        .then((config) => {
          if (config.rtc_configuration) {
            this.rtcConfig = config.rtc_configuration;
          }
          if (config.avatar_config) {
            this.avatarType = config.avatar_config.avatar_type;
            this.avatarWSRoute = config.avatar_config.avatar_ws_route;
            this.avatarAssetsPath = config.avatar_config.avatar_assets_path;
          }
          if (config.track_constraints) {
            this.trackConstraints = config.track_constraints;
          }
        })
        .catch(() => {
          message.error('服务端链接失败，请检查是否能正确访问到 OpenAvatarChat 服务端');
        });
    },

    // 切换摄像头开关
    handleCameraOff() {
      this.cameraOff = !this.cameraOff;
      this.stream?.getTracks().forEach((track) => {
        if (track.kind.includes('video')) track.enabled = !this.cameraOff;
      });
    },

    // 切换麦克风开关
    handleMicMuted() {
      this.micMuted = !this.micMuted;
      this.stream?.getTracks().forEach((track) => {
        if (track.kind.includes('audio')) track.enabled = !this.micMuted;
      });
    },

    // 切换扬声器静音（lam 头像用）
    handleVolumeMute() {
      this.volumeMuted = !this.volumeMuted;
      if (this.avatarType === 'lam') {
        this.localAvatarRenderer?.setAvatarMute(this.volumeMuted);
      }
    },

    // 设备切换时重新获取流
    async handleDeviceChange(deviceId: string) {
      const devices = await getDevices();
      this.devices = devices;

      let videoDeviceId = this.selectedVideoDevice?.deviceId || '';
      let audioDeviceId = this.selectedAudioDevice?.deviceId || '';

      if (this.availableVideoDevices.find((d) => d.deviceId === deviceId)) {
        videoDeviceId = deviceId;
        this.cameraOff = false;
      } else if (this.availableAudioDevices.find((d) => d.deviceId === deviceId)) {
        audioDeviceId = deviceId;
        this.micMuted = false;
      }

      this.fillStream(audioDeviceId, videoDeviceId);
    },

    // 切换字幕显示
    handleSubtitleToggle() {
      this.showChatRecords = !this.showChatRecords;
    },

    // 更新可用设备
    async updateAvailableDevices() {
      const devices = await getDevices();
      this.availableVideoDevices = setAvailableDevices(devices, 'videoinput');
      this.availableAudioDevices = setAvailableDevices(devices, 'audioinput');
    },

    // 填充媒体流（选择特定设备 ID）
    async fillStream(audioDeviceId: string, videoDeviceId: string) {
      const { devices } = this; // 从当前实例获取设备列表（可能包含所有音视频设备）
      const visionState = useVisionStore();
      const node = visionState.localVideoRef; // 获取本地视频元素的DOM引用（用于播放流）

      // 检查是否有可用麦克风及麦克风权限
      this.hasMic =
        devices.some((d) => d.kind === 'audioinput' && d.deviceId) && this.hasMicPermission;
      // 检查是否有可用摄像头及摄像头权限
      this.hasCamera =
        devices.some((d) => d.kind === 'videoinput' && d.deviceId) && this.hasCameraPermission;

      // 音频设备配置：如果指定了audioDeviceId，则精确使用该设备；否则使用是否有麦克风的检查结果
      await getStream(
        audioDeviceId ? { deviceId: { exact: audioDeviceId } } : this.hasMic,
        videoDeviceId ? { deviceId: { exact: videoDeviceId } } : this.hasCamera,
        this.trackConstraints,
      )
        .then(async (local_stream) => {
          this.stream = local_stream; // 将获取到的流存储到实例
          this.updateAvailableDevices();
        })
        .then(() => {
          // 从流的轨道中获取使用的设备ID
          const used_devices = this.stream!.getTracks().map(
            (track) => track.getSettings()?.deviceId,
          );
          // 匹配设备列表，确定当前选中的音视频设备
          used_devices.forEach((device_id) => {
            const used_device = devices.find((d) => d.deviceId === device_id);
            if (used_device?.kind.includes('video')) {
              this.selectedVideoDevice = used_device; // 记录当前使用的摄像头
            } else if (used_device?.kind.includes('audio')) {
              this.selectedAudioDevice = used_device; // 记录当前使用的麦克风
            }
          });
          // 如果没有选中的视频设备，默认使用第一个可用视频设备
          if (!this.selectedVideoDevice) {
            this.selectedVideoDevice = this.availableVideoDevices[0];
          }
        })
        .catch((e) => {
          console.error('image.no_webcam_support', e);
        })
        .finally(() => {
          // 确保stream存在（即使获取失败，也初始化一个空流）
          if (!this.stream) {
            this.stream = new MediaStream();
          }
          // 如果流中没有音频轨道，添加模拟音频轨道
          if (!this.stream.getTracks().find((item) => item.kind === 'audio')) {
            this.stream.addTrack(createSimulatedAudioTrack());
          }
          // 如果流中没有视频轨道，添加模拟视频轨道
          if (!this.stream.getTracks().find((item) => item.kind === 'video')) {
            this.stream.addTrack(createSimulatedVideoTrack());
          }
          // 更新状态：标记摄像头已访问
          this.webcamAccessed = true;
          // 存储本地流
          this.localStream = this.stream;
          // 将流绑定到视频元素并播放
          if (node) {
            node.srcObject = this.localStream;
            node.muted = true;
            node.play();
          }
        });
    },

    // 开始/停止 WebRTC
    /**
     * 启动 WebRTC 连接
     * 支持三种状态处理：
     *  - closed: 新建连接
     *  - waiting: 等待中，不允许重复操作
     *  - open: 已打开连接，调用则关闭
     */
    async startWebRTC() {
      const visionState = useVisionStore();

      // 1. 当前连接状态为关闭时，初始化 WebRTC
      if (this.streamState === 'closed') {
        // 清空聊天记录
        this.chatRecords = [];

        // 创建新的 RTCPeerConnection 实例
        this.peerConnection = new RTCPeerConnection();

        // 监听连接状态变化
        this.peerConnection.addEventListener('connectionstatechange', async () => {
          console.log('connectionstatechange', this.peerConnection!.connectionState, this.peerConnection!);
          switch (this.peerConnection!.connectionState) {
            case 'connected':
              // 连接成功，更新状态
              this.streamState = StreamState.open;
              break;
            case 'disconnected':
              // 断开连接，停止 PeerConnection 并更新状态
              this.streamState = StreamState.closed;
              stop(this.peerConnection!);
              break;
          }
        });

        // 设置状态为等待中，防止重复操作
        this.streamState = StreamState.waiting

        // 2. 调用 setupWebRTC 绑定本地流和远端 video 元素
        await setupWebRTC(this.stream!, this.peerConnection!, visionState.remoteVideoRef!)
          .then(([dataChannel, webRTCId]) => {
            // 连接成功后，更新状态
            this.streamState = StreamState.open;

            // 保存 WebRTC ID 和数据通道
            this.webRTCId = webRTCId as string;
            this.chatDataChannel = dataChannel as any;

            // 3. 如果配置了 Avatar，初始化 WebSocket 和渲染
            if (this.avatarType && this.avatarWSRoute) {
              const ws = this.initWebsocket(this.avatarWSRoute, this.webRTCId);

              // Lam 类型 Avatar，启动 Gaussian 渲染器
              if (this.avatarType === 'lam') {
                this.localAvatarRenderer = this.doGaussianRender(ws);
              }
            }
          })
          .catch((e) => {
            // WebRTC 建立失败，回退状态并提示错误
            this.streamState = StreamState.closed;
            message.error(e);
          });

        // 2. 当前状态为等待中时，不允许重复操作
      } else if (this.streamState === 'waiting') {
        // do nothing
      } else {
        // 3. 当前状态已打开（open），则关闭连接并重置状态
        stop(this.peerConnection!);
        this.streamState = StreamState.closed;

        // 清空相关数据
        this.chatRecords = [];
        this.chatDataChannel = null;
        this.replying = false;

        // 切换为纯接收模式，避免再次弹权限框
        await this.accessDevice();

        // 如果是 Lam Avatar，退出渲染并重置加载进度
        if (this.avatarType === 'lam') {
          this.localAvatarRenderer?.exit();
          this.gsLoadPercent = 0;
        }
      }
    },
    // 初始化 WebSocket
    initWebsocket(ws_route: string, webRTCId: string) {
      const ws = new WS(
        `${window.location.protocol.includes('https') ? 'wss' : 'ws'}://${window.location.host}${ws_route}/${webRTCId}`,
      );
      ws.on(WsEventTypes.WS_OPEN, () => console.log('socket opened'));
      ws.on(WsEventTypes.WS_CLOSE, () => console.log('socket closed'));
      ws.on(WsEventTypes.WS_ERROR, (event) => console.log('socket error', event));
      ws.on(WsEventTypes.WS_MESSAGE, (data) => console.log('socket on message', data));
      return ws;
    },

    // 执行高斯头像渲染
    doGaussianRender(ws: WS) {
      const visionState = useVisionStore();
      const gaussianAvatar = new GaussianAvatar({
        container: visionState.remoteVideoContainerRef!,
        assetsPath: this.avatarAssetsPath,
        ws,
        loadProgress: (progress) => {
          this.gsLoadPercent = progress;
        },
      });
      gaussianAvatar.start();
      return gaussianAvatar;
    },
  },
});
