{"name": "open-avatar-chat-webui", "version": "4.0.0", "private": true, "type": "module", "scripts": {"build": "vue-tsc && vite build", "ci:eslint": "eslint -f json src -o ./.ci/eslint.json", "ci:test": "vitest -c ./vitest.config.ts --coverage", "dev": "vite", "eslint": "eslint --fix --ext .js,.ts,.vue src", "format": "prettier --write --cache --parser typescript \"**/*.[tj]s?(x)\"", "lint": "eslint . && stylelint --allow-empty-input \"**/*.{css,less,scss}\"", "lint-staged": "lint-staged", "lint:fix": "prettier --write . && eslint --fix . && stylelint --allow-empty-input --fix \"**/*.{css,less,scss}\"", "local": "sudo vite", "prepare": "husky", "preview": "vite preview", "test": "vitest"}, "lint-staged": {"*.{cjs,cts,js,jsx,mjs,mts,ts,tsx,vue}": "eslint --fix", "*.{cjs,css,cts,html,js,json,jsx,less,md,mjs,mts,scss,ts,tsx,vue,yaml,yml}": "prettier --write"}, "prettier": "prettier-config-ali", "stylelint": {"extends": ["stylelint-config-ali", "stylelint-prettier/recommended"]}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "base64-js": "^1.5.1", "buffer": "^6.0.3", "click-outside-vue3": "^4.0.1", "eventemitter3": "^5.0.1", "gaussian-splat-renderer-for-lam": "^0.0.8", "hls.js": "^1.5.16", "mrmime": "^2.0.0", "nanoid": "^5.1.5", "p-queue": "^8.0.1", "pinia": "^3.0.3", "python-struct": "^1.1.3", "vue": "^3.5.17", "vue-i18n": "^11.1.9"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.1", "@types/node": "^20.17.6", "@types/python-struct": "^1.0.4", "@vitejs/plugin-legacy": "^7.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-config-ali": "^16.3.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-config-ali": "^1.3.4", "simple-git-hooks": "^2.13.0", "stylelint": "^16.22.0", "stylelint-config-ali": "^2.1.2", "stylelint-config-standard": "^38.0.0", "stylelint-prettier": "^5.0.3", "terser": "^5.36.0", "typescript": "5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.1", "vite-plugin-eslint2": "^5.0.4", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-stylelint": "^6.0.2", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.1"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}