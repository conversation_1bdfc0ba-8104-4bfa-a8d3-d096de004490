export enum TYVoiceChatState {
  Idle = 'Idle',
  Listening = 'Listening',
  Responding = 'Responding',
  Thinking = 'Thinking',
}

export enum StreamState {
  closed = 'closed',
  open = 'open',
  waiting = 'waiting',
}

// WebRTC 连接模式配置
export interface WebRTCConfig {
  enableLocalStream: boolean // 是否启用本地流（摄像头/麦克风）
  enableLocalVideo: boolean // 是否启用本地视频
  enableLocalAudio: boolean // 是否启用本地音频
  autoRequestPermissions: boolean // 是否自动请求设备权限
  showLocalVideo: boolean // 是否显示本地视频预览
  showDeviceControls: boolean // 是否显示设备控制按钮
}

// 默认配置 - 双向模式
export const DEFAULT_WEBRTC_CONFIG: WebRTCConfig = {
  enableLocalStream: true,
  enableLocalVideo: true,
  enableLocalAudio: true,
  autoRequestPermissions: true,
  showLocalVideo: true,
  showDeviceControls: true,
}

// 仅接收模式配置
export const RECEIVE_ONLY_CONFIG: WebRTCConfig = {
  enableLocalStream: false,
  enableLocalVideo: false,
  enableLocalAudio: false,
  autoRequestPermissions: false,
  showLocalVideo: false,
  showDeviceControls: false,
}
