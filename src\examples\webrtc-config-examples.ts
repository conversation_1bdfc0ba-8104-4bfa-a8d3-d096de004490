/**
 * WebRTC 配置示例
 * 展示如何配置不同的 WebRTC 模式
 */

import { useVideoChatStore } from '@/store'
import { RECEIVE_ONLY_CONFIG, DEFAULT_WEBRTC_CONFIG, WebRTCConfig } from '@/interface/voiceChat'

// 示例1: 仅接收模式 - 只接收远程流，不发送本地流
export function setupReceiveOnlyMode() {
  const videoChatStore = useVideoChatStore()
  
  // 设置为仅接收模式
  videoChatStore.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
  
  console.log('已设置为仅接收模式：')
  console.log('- 不会请求摄像头/麦克风权限')
  console.log('- 不会发送本地音视频流')
  console.log('- 不会显示本地视频预览')
  console.log('- 不会显示设备控制按钮')
  console.log('- 只会接收和显示远程流')
}

// 示例2: 双向通信模式（默认）
export function setupBidirectionalMode() {
  const videoChatStore = useVideoChatStore()
  
  // 设置为双向模式
  videoChatStore.setWebRTCConfig(DEFAULT_WEBRTC_CONFIG)
  
  console.log('已设置为双向通信模式：')
  console.log('- 会请求摄像头/麦克风权限')
  console.log('- 会发送本地音视频流')
  console.log('- 会显示本地视频预览')
  console.log('- 会显示设备控制按钮')
  console.log('- 支持双向音视频通信')
}

// 示例3: 自定义配置 - 只发送音频，不发送视频
export function setupAudioOnlyMode() {
  const videoChatStore = useVideoChatStore()
  
  const audioOnlyConfig: WebRTCConfig = {
    enableLocalStream: true,
    enableLocalVideo: false,  // 不启用本地视频
    enableLocalAudio: true,   // 启用本地音频
    autoRequestPermissions: true,
    showLocalVideo: false,    // 不显示本地视频预览
    showDeviceControls: true, // 显示设备控制（但只有音频控制）
  }
  
  videoChatStore.setWebRTCConfig(audioOnlyConfig)
  
  console.log('已设置为仅音频模式：')
  console.log('- 会请求麦克风权限，不请求摄像头权限')
  console.log('- 只发送音频流，不发送视频流')
  console.log('- 不显示本地视频预览')
  console.log('- 显示音频相关的设备控制')
}

// 示例4: 自定义配置 - 显示本地视频但不发送
export function setupLocalPreviewOnlyMode() {
  const videoChatStore = useVideoChatStore()
  
  const previewOnlyConfig: WebRTCConfig = {
    enableLocalStream: false, // 不发送本地流
    enableLocalVideo: true,   // 启用本地视频（用于预览）
    enableLocalAudio: false,  // 不启用本地音频
    autoRequestPermissions: true,
    showLocalVideo: true,     // 显示本地视频预览
    showDeviceControls: true, // 显示设备控制
  }
  
  videoChatStore.setWebRTCConfig(previewOnlyConfig)
  
  console.log('已设置为本地预览模式：')
  console.log('- 会请求摄像头权限')
  console.log('- 显示本地视频预览')
  console.log('- 不发送本地流到远程')
  console.log('- 可以接收远程流')
}

// 示例5: 在组件中使用配置
export function useWebRTCConfig() {
  const videoChatStore = useVideoChatStore()
  
  // 根据 URL 参数或其他条件设置模式
  const urlParams = new URLSearchParams(window.location.search)
  const mode = urlParams.get('mode')
  
  switch (mode) {
    case 'receive-only':
      setupReceiveOnlyMode()
      break
    case 'audio-only':
      setupAudioOnlyMode()
      break
    case 'preview-only':
      setupLocalPreviewOnlyMode()
      break
    default:
      setupBidirectionalMode()
      break
  }
  
  return videoChatStore
}

// 示例6: 动态切换模式
export function switchToReceiveOnlyMode() {
  const videoChatStore = useVideoChatStore()
  
  // 如果当前有连接，先断开
  if (videoChatStore.streamState === 'open') {
    // 这里应该调用断开连接的方法
    console.log('断开当前连接...')
  }
  
  // 切换到仅接收模式
  videoChatStore.setWebRTCConfig(RECEIVE_ONLY_CONFIG)
  
  // 重新初始化
  videoChatStore.accessDevice()
  
  console.log('已切换到仅接收模式')
}

// 示例7: 检查当前配置
export function checkCurrentConfig() {
  const videoChatStore = useVideoChatStore()
  const config = videoChatStore.webrtcConfig
  
  console.log('当前 WebRTC 配置：', {
    enableLocalStream: config.enableLocalStream,
    enableLocalVideo: config.enableLocalVideo,
    enableLocalAudio: config.enableLocalAudio,
    autoRequestPermissions: config.autoRequestPermissions,
    showLocalVideo: config.showLocalVideo,
    showDeviceControls: config.showDeviceControls,
  })
  
  return config
}
