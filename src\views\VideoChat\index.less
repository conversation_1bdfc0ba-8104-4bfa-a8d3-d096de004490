.chat-input-wrapper {
  // position: absolute;
  margin: 0 70px;
  transition: width 0.1s ease;
}
.page-container {
  height: 100%;
  width: 100%;
  padding: 32px;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}
.content-container {
  height: 100%;
  max-width: calc(100%);
}
.video-container {
  position: relative;
  aspect-ratio: 9 / 16;
  max-width: calc(100% - 70px - 64px);
  height: 85%;
  margin: 0 70px;
  .local-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 32px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    background: #fff;
    transition: all 0.3s ease;
  }
  .local-video-container.scaled {
    top: auto;
    left: 12px;
    bottom: 12px;
    width: calc(50% - 12px);
    height: auto;
  }
  .remote-video-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 32px;
    transition: all 0.3s linear;
    background: #fff;
  }
  .local-video {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center center;
  }
  .remote-video {
    width: 101%;
    height: 100%;
    object-fit: cover;
  }

  .remote-canvas {
    width: 100%;
    height: 100%;
  }
}
.actions {
  position: absolute;
  z-index: 2;
  left: calc(100% + 12px);
  bottom: 0;
}
.chat-records-container {
  height: 85%;
  overflow: auto;
  aspect-ratio: 9 / 16;
  z-index: 1;

  &.inline {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 10px;
  }
}
